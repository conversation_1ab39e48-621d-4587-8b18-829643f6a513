const GroupMember = require('../models/GroupMemberPrisma');
const ChatGroup = require('../models/ChatGroupPrisma');
const Message = require('../models/MessagePrisma');
const ResponseHelper = require('../utils/response');

/**
 * 自动加入群组中间件
 * 当用户访问群组时，如果不是群组成员就自动加入群组
 * 
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next 下一个中间件函数
 */
const autoJoinGroup = async (req, res, next) => {
  try {
    // 检查是否有用户信息（需要先通过认证中间件）
    if (!req.user || !req.user.id) {
      return ResponseHelper.unauthorized(res, '请先登录');
    }

    // 从路由参数中获取群组ID
    const groupId = parseInt(req.params.id);
    if (!groupId || isNaN(groupId)) {
      return ResponseHelper.validationError(res, ['无效的群组ID']);
    }

    const userId = req.user.id;

    // 检查群组是否存在
    const group = await ChatGroup.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群组不存在');
    }

    // 检查用户是否已经是群组成员
    const isMember = await GroupMember.isMember(groupId, userId);
    
    if (!isMember) {
      try {
        console.log(`🔄 用户 ${userId} 自动加入群组 ${groupId}`);
        
        // 自动加入群组
        await GroupMember.create({
          group_id: groupId,
          user_id: userId,
          role: 'member'
        });

        // 创建系统消息
        const userNickname = req.user.nickname || `用户${userId}`;
        await Message.create({
          group_id: groupId,
          sender_id: userId,
          message_type: 'system',
          content: `${userNickname} 加入了群聊`
        });

        console.log(`✅ 用户 ${userId} 成功自动加入群组 ${groupId}`);
      } catch (error) {
        // 如果是重复加入的错误，忽略（可能是并发请求导致的）
        if (error.message && error.message.includes('已经是群组成员')) {
          console.log(`⚠️ 用户 ${userId} 已经是群组 ${groupId} 成员，跳过加入操作`);
        } else {
          console.error(`❌ 用户 ${userId} 自动加入群组 ${groupId} 失败:`, error);
          return ResponseHelper.serverError(res, '自动加入群组失败', error);
        }
      }
    }

    // 继续执行下一个中间件
    next();
  } catch (error) {
    console.error('自动加入群组中间件错误:', error);
    ResponseHelper.serverError(res, '处理群组访问失败', error);
  }
};

/**
 * 可选的自动加入群组中间件
 * 用于支持未登录用户查看的接口，如果用户已登录则尝试自动加入群组
 * 
 * @param {Object} req Express请求对象
 * @param {Object} res Express响应对象
 * @param {Function} next 下一个中间件函数
 */
const optionalAutoJoinGroup = async (req, res, next) => {
  try {
    // 如果用户未登录，直接跳过
    if (!req.user || !req.user.id) {
      return next();
    }

    // 从路由参数中获取群组ID
    const groupId = parseInt(req.params.id);
    if (!groupId || isNaN(groupId)) {
      return ResponseHelper.validationError(res, ['无效的群组ID']);
    }

    const userId = req.user.id;

    // 检查群组是否存在
    const group = await ChatGroup.findById(groupId);
    if (!group) {
      return ResponseHelper.notFound(res, '群组不存在');
    }

    // 检查用户是否已经是群组成员
    const isMember = await GroupMember.isMember(groupId, userId);
    
    if (!isMember) {
      try {
        console.log(`🔄 用户 ${userId} 自动加入群组 ${groupId} (可选模式)`);
        
        // 自动加入群组
        await GroupMember.create({
          group_id: groupId,
          user_id: userId,
          role: 'member'
        });

        // 创建系统消息
        const userNickname = req.user.nickname || `用户${userId}`;
        await Message.create({
          group_id: groupId,
          sender_id: userId,
          message_type: 'system',
          content: `${userNickname} 加入了群聊`
        });

        console.log(`✅ 用户 ${userId} 成功自动加入群组 ${groupId} (可选模式)`);
      } catch (error) {
        // 如果是重复加入的错误，忽略（可能是并发请求导致的）
        if (error.message && error.message.includes('已经是群组成员')) {
          console.log(`⚠️ 用户 ${userId} 已经是群组 ${groupId} 成员，跳过加入操作 (可选模式)`);
        } else {
          console.error(`❌ 用户 ${userId} 自动加入群组 ${groupId} 失败 (可选模式):`, error);
          // 在可选模式下，不阻止请求继续执行
        }
      }
    }

    // 继续执行下一个中间件
    next();
  } catch (error) {
    console.error('可选自动加入群组中间件错误:', error);
    // 在可选模式下，不阻止请求继续执行
    next();
  }
};

module.exports = {
  autoJoinGroup,
  optionalAutoJoinGroup
};
