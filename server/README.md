# 甩单系统服务端

这是甩单系统的后端API服务，基于Node.js + Express + MySQL构建。

## 功能特性

### 已完成功能 ✅

1. **项目与数据库初始化**
   - Express项目框架搭建
   - 环境变量配置管理
   - MySQL数据库连接池
   - 数据库表结构创建
   - Model层封装

2. **用户认证模块**
   - 微信静默登录 (POST /api/v1/auth/login)
   - 更新用户信息 (PUT /api/v1/users/me)
   - 获取当前用户信息 (GET /api/v1/users/me)
   - JWT认证中间件

3. **挂牌信息模块**
   - 发布新信息 (POST /api/v1/listings)
   - 获取信息列表 (GET /api/v1/listings)
   - 获取信息详情 (GET /api/v1/listings/:id)
   - 获取我发布的信息 (GET /api/v1/users/me/listings)

## 快速开始

### 1. 环境准备

确保已安装：
- Node.js (v14+)
- MySQL (v5.7+)
- npm 或 yarn

### 2. 安装依赖

```bash
cd server
npm install
```

### 3. 环境配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库和微信小程序信息：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=shuaiDanSystem

# JWT配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# 微信小程序配置
WECHAT_APP_ID=your_wechat_app_id
WECHAT_APP_SECRET=your_wechat_app_secret

# 服务器配置
PORT=3000
NODE_ENV=development
```

### 4. 数据库初始化

创建数据库：
```sql
CREATE DATABASE shuaiDanSystem CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

执行数据库初始化脚本：
```bash
npm run init-db
```

### 5. 启动服务

开发模式（自动重启）：
```bash
npm run dev
```

生产模式：
```bash
npm start
```

服务启动后访问：http://localhost:3000/health

## API文档

### 认证接口

#### 微信登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "code": "wx_login_code",
  "inviterId": 123  // 可选，邀请者ID
}
```

#### 刷新Token
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "token": "old_jwt_token"
}
```

### 用户接口

#### 获取当前用户信息
```http
GET /api/v1/users/me
Authorization: Bearer your_jwt_token
```

#### 更新用户信息
```http
PUT /api/v1/users/me
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
  "nickname": "用户昵称",
  "avatar_url": "头像URL",
  "phone_number": "手机号"
}
```

#### 获取我的发布
```http
GET /api/v1/users/me/listings?page=1&pageSize=10
Authorization: Bearer your_jwt_token
```

### 挂牌信息接口

#### 获取信息列表
```http
GET /api/v1/listings?page=1&pageSize=10&status=在售&listing_type=公司
```

#### 获取信息详情
```http
GET /api/v1/listings/1
```

#### 发布新信息
```http
POST /api/v1/listings
Authorization: Bearer your_jwt_token
Content-Type: application/json

{
  "listing_type": "公司",
  "company_name": "公司名称",
  "price": 50000,
  "is_negotiable": 1,
  "registration_province": "省份",
  "registration_city": "城市",
  "establishment_date": "2020-01-01",
  "registered_capital_range": "100-500万",
  "paid_in_status": "已实缴",
  "company_type": "普通公司",
  "tax_status": "一般纳税人",
  "bank_account_status": "已开户",
  "description": "详细描述"
}
```

## 项目结构

```
server/
├── config/          # 配置文件
│   └── db.js        # 数据库配置
├── middleware/      # 中间件
│   └── auth.js      # JWT认证中间件
├── models/          # 数据模型
│   ├── User.js      # 用户模型
│   └── Listing.js   # 挂牌信息模型
├── routes/          # 路由
│   ├── auth.js      # 认证路由
│   ├── users.js     # 用户路由
│   └── listings.js  # 挂牌信息路由
├── scripts/         # 脚本
│   └── init-database.js  # 数据库初始化
├── test/            # 测试
│   └── api-test.js  # API测试
├── utils/           # 工具类
│   ├── wechat.js    # 微信API工具
│   ├── response.js  # 响应工具
│   └── validator.js # 数据验证工具
├── .env.example     # 环境变量模板
├── server.js        # 服务入口
└── package.json     # 项目配置
```

## 开发命令

```bash
# 安装依赖
npm install

# 开发模式启动
npm run dev

# 生产模式启动
npm start

# 初始化数据库
npm run init-db

# 语法检查
node -c server.js
```

## 测试

运行API测试：
```bash
# 确保服务已启动
npm run dev

# 在另一个终端运行测试
node test/api-test.js
```

## 注意事项

1. **微信配置**：需要在微信公众平台配置小程序的AppID和AppSecret
2. **数据库权限**：确保数据库用户有创建表和插入数据的权限
3. **JWT密钥**：生产环境请使用强密钥
4. **HTTPS**：生产环境建议使用HTTPS
5. **日志**：生产环境建议配置日志系统

## 下一步开发

- [ ] 邀请与激活模块
- [ ] 支付模块（微信支付）
- [ ] 套餐管理
- [ ] 订单管理
- [ ] 管理后台API
- [ ] 文件上传功能
- [ ] 消息推送
- [ ] 数据统计
