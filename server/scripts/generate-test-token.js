const { generateToken } = require('../middleware/auth');

/**
 * 生成测试用户的JWT token
 */
function generateTestToken() {
  const payload = {
    user_id: 6, // 测试用户ID
    openid: 'test_openid_12345'
  };
  
  const token = generateToken(payload);
  console.log('测试用户JWT Token:');
  console.log(token);
  return token;
}

// 如果直接运行此脚本
if (require.main === module) {
  generateTestToken();
}

module.exports = {
  generateTestToken
};
