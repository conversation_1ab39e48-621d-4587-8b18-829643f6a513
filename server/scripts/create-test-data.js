const { query } = require('../config/db');

/**
 * 创建测试数据
 */
async function createTestData() {
  try {
    console.log('🚀 开始创建测试数据...');

    // 1. 创建测试群组
    console.log('📝 创建测试群组...');
    await query(`
      INSERT IGNORE INTO chat_groups (id, group_name, description) VALUES 
      (1, '全国接单总群', '全国范围内的接单信息交流群'),
      (2, '北京同行群', '北京地区同行交流群'),
      (3, '上海同行群', '上海地区同行交流群'),
      (4, '广州同行群', '广州地区同行交流群'),
      (5, '深圳同行群', '深圳地区同行交流群')
    `);

    // 2. 创建测试用户
    console.log('📝 创建测试用户...');
    await query(`
      INSERT IGNORE INTO users (id, openid, nickname, status, publishing_credits) VALUES 
      (1, 'test_openid_1', '张三', 'active', 10),
      (2, 'test_openid_2', '李四', 'active', 5),
      (3, 'test_openid_3', '王五', 'active', 8),
      (4, 'test_openid_4', '赵六', 'active', 3)
    `);

    // 3. 创建群组成员关系
    console.log('📝 创建群组成员关系...');
    await query(`
      INSERT IGNORE INTO group_members (group_id, user_id, role) VALUES 
      (1, 1, 'owner'),
      (1, 2, 'member'),
      (1, 3, 'member'),
      (1, 4, 'member'),
      (2, 1, 'member'),
      (2, 2, 'admin'),
      (3, 3, 'member'),
      (4, 4, 'member')
    `);

    // 4. 创建测试消息
    console.log('📝 创建测试消息...');
    await query(`
      INSERT IGNORE INTO messages (id, group_id, sender_id, message_type, content) VALUES 
      (1, 1, 1, 'system', '张三 邀请 李四 加入群聊'),
      (2, 1, 1, 'system', '李四 邀请 王五 加入群聊'),
      (3, 1, 2, 'text', '客户需求：买一个个体\\n要求：1年以上 最好为重庆市江北区\\n他们做卖茶叶的 最好是跟这个相关的'),
      (4, 1, 3, 'text', '收一家带三类医疗的科技公司，高新区最好'),
      (5, 1, 4, 'text', '有没有做餐饮的个体户，要求在成都市范围内'),
      (6, 1, 1, 'text', '我这里有一家科技公司，注册资本100万，有需要的联系我'),
      (7, 2, 2, 'text', '北京这边有什么好的资源吗？'),
      (8, 2, 1, 'text', '我手上有几家北京的公司，可以私聊详谈'),
      (9, 3, 3, 'text', '上海地区的朋友们，大家好！'),
      (10, 4, 4, 'text', '广州这边的市场怎么样？')
    `);

    console.log('✅ 测试数据创建完成！');
    console.log('📊 数据统计:');
    
    // 统计数据
    const groupCount = await query('SELECT COUNT(*) as count FROM chat_groups');
    const userCount = await query('SELECT COUNT(*) as count FROM users');
    const messageCount = await query('SELECT COUNT(*) as count FROM messages');
    
    console.log(`   - 群组数量: ${groupCount[0].count}`);
    console.log(`   - 用户数量: ${userCount[0].count}`);
    console.log(`   - 消息数量: ${messageCount[0].count}`);

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
    throw error;
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  createTestData()
    .then(() => {
      console.log('🎉 测试数据创建完成！');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 创建测试数据失败:', error);
      process.exit(1);
    });
}

module.exports = { createTestData };
