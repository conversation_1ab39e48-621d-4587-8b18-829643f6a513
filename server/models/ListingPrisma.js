const { prisma } = require('../config/prisma');

class ListingPrisma {
  // 状态值映射：中文 -> 英文枚举
  static STATUS_MAP = {
    '在售': 'ON_SALE',
    '已售': 'SOLD',
    '下架': 'OFF_SHELF'
  };

  // 反向状态值映射：英文枚举 -> 中文
  static STATUS_REVERSE_MAP = {
    'ON_SALE': '在售',
    'SOLD': '已售',
    'OFF_SHELF': '下架'
  };

  // 商品类型映射：中文 -> 英文枚举
  static TYPE_MAP = {
    '公司': 'COMPANY',
    '个体户': 'INDIVIDUAL',
    '代账户': 'ACCOUNTING'
  };

  // 反向商品类型映射：英文枚举 -> 中文
  static TYPE_REVERSE_MAP = {
    'COMPANY': '公司',
    'INDIVIDUAL': '个体户',
    'ACCOUNTING': '代账户'
  };
  constructor(data) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.listing_type = data.listing_type;
    this.company_name = data.company_name;
    this.status = data.status;
    this.price = data.price;
    this.is_negotiable = data.is_negotiable;
    this.registration_province = data.registration_province;
    this.registration_city = data.registration_city;
    this.establishment_date = data.establishment_date;
    this.registered_capital_range = data.registered_capital_range;
    this.paid_in_status = data.paid_in_status;
    this.company_type = data.company_type;
    this.tax_status = data.tax_status;
    this.bank_account_status = data.bank_account_status;
    this.has_trademark = data.has_trademark;
    this.has_patent = data.has_patent;
    this.has_software_copyright = data.has_software_copyright;
    this.has_license_plate = data.has_license_plate;
    this.has_social_security = data.has_social_security;
    this.shareholder_background = data.shareholder_background;
    this.has_bidding_history = data.has_bidding_history;
    this.description = data.description;
    this.expires_at = data.expires_at;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
    
    // 关联的用户信息（如果有）
    this.publisher_nickname = data.users?.nickname || data.publisher_nickname;
    this.users = data.users; // 完整的用户对象
  }

  /**
   * 创建新的挂牌信息
   * @param {Object} listingData 挂牌信息数据
   * @returns {Promise<ListingPrisma>}
   */
  static async create(listingData) {
    const listing = await prisma.listings.create({
      data: {
        user_id: BigInt(listingData.user_id),
        listing_type: listingData.listing_type,
        company_name: listingData.company_name,
        status: listingData.status || 'ON_SALE',
        price: listingData.price || null,
        is_negotiable: listingData.is_negotiable || false,
        registration_province: listingData.registration_province || null,
        registration_city: listingData.registration_city || null,
        establishment_date: listingData.establishment_date || null,
        registered_capital_range: listingData.registered_capital_range || null,
        paid_in_status: listingData.paid_in_status || null,
        company_type: listingData.company_type || null,
        tax_status: listingData.tax_status || null,
        bank_account_status: listingData.bank_account_status || null,
        has_trademark: listingData.has_trademark || false,
        has_patent: listingData.has_patent || false,
        has_software_copyright: listingData.has_software_copyright || false,
        has_license_plate: listingData.has_license_plate || false,
        has_social_security: listingData.has_social_security || false,
        shareholder_background: listingData.shareholder_background || null,
        has_bidding_history: listingData.has_bidding_history || false,
        description: listingData.description || null,
        expires_at: listingData.expires_at || null
      },
      include: {
        users: true
      }
    });
    return new ListingPrisma(listing);
  }

  /**
   * 根据ID查找挂牌信息
   * @param {number} id 挂牌信息ID
   * @returns {Promise<ListingPrisma|null>}
   */
  static async findById(id) {
    const listing = await prisma.listings.findUnique({
      where: { id: BigInt(id) },
      include: {
        users: true
      }
    });
    return listing ? new ListingPrisma(listing) : null;
  }

  /**
   * 获取挂牌信息列表（分页）
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const {
      page = 1,
      pageSize = 10,
      status = '在售',
      listing_type,
      registration_city,
      company_type,
      tax_status,
      search
    } = options;

    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const skip = (pageNum - 1) * pageSizeNum;

    // 构建where条件
    const where = {};

    // 转换状态值
    if (status) {
      where.status = this.STATUS_MAP[status] || status;
    }

    // 转换商品类型
    if (listing_type) {
      where.listing_type = this.TYPE_MAP[listing_type] || listing_type;
    }


    
    if (registration_city) {
      where.registration_city = registration_city;
    }
    
    if (company_type) {
      where.company_type = company_type;
    }
    
    if (tax_status) {
      where.tax_status = tax_status;
    }
    
    if (search) {
      where.OR = [
        { company_name: { contains: search } },
        { description: { contains: search } }
      ];
    }

    // 并行查询总数和列表数据
    const [total, listings] = await Promise.all([
      prisma.listings.count({ where }),
      prisma.listings.findMany({
        where,
        include: {
          users: {
            select: {
              id: true,
              nickname: true,
              avatar_url: true
            }
          }
        },
        orderBy: { created_at: 'desc' },
        skip,
        take: pageSizeNum
      })
    ]);

    return {
      data: listings.map(listing => new ListingPrisma(listing)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 获取用户发布的挂牌信息
   * @param {number} userId 用户ID
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getUserListings(userId, options = {}) {
    const { page = 1, pageSize = 10, status } = options;
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const skip = (pageNum - 1) * pageSizeNum;

    const where = {
      user_id: BigInt(userId)
    };

    if (status) {
      where.status = status;
    }

    const [total, listings] = await Promise.all([
      prisma.listings.count({ where }),
      prisma.listings.findMany({
        where,
        orderBy: { created_at: 'desc' },
        skip,
        take: pageSizeNum
      })
    ]);

    return {
      data: listings.map(listing => new ListingPrisma(listing)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 更新挂牌信息
   * @param {number} id 挂牌信息ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<ListingPrisma>}
   */
  static async update(id, updateData) {
    // 处理BigInt字段
    const data = { ...updateData };
    if (data.user_id !== undefined) {
      data.user_id = BigInt(data.user_id);
    }

    const listing = await prisma.listings.update({
      where: { id: BigInt(id) },
      data,
      include: {
        users: true
      }
    });
    return new ListingPrisma(listing);
  }

  /**
   * 删除挂牌信息
   * @param {number} id 挂牌信息ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    await prisma.listings.delete({
      where: { id: BigInt(id) }
    });
    return true;
  }

  /**
   * 更新挂牌状态
   * @param {number} id 挂牌信息ID
   * @param {string} status 新状态
   * @returns {Promise<ListingPrisma>}
   */
  static async updateStatus(id, status) {
    return await ListingPrisma.update(id, { status });
  }

  /**
   * 获取挂牌统计信息
   * @returns {Promise<Object>}
   */
  static async getStats() {
    const [total, onSale, sold, offShelf] = await Promise.all([
      prisma.listings.count(),
      prisma.listings.count({ where: { status: 'ON_SALE' } }),
      prisma.listings.count({ where: { status: 'SOLD' } }),
      prisma.listings.count({ where: { status: 'OFF_SHELF' } })
    ]);

    return { total, onSale, sold, offShelf };
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id.toString(),
      user_id: this.user_id.toString(),
      listing_type: ListingPrisma.TYPE_REVERSE_MAP[this.listing_type] || this.listing_type,
      company_name: this.company_name,
      status: ListingPrisma.STATUS_REVERSE_MAP[this.status] || this.status,
      price: this.price ? parseFloat(this.price) : null,
      is_negotiable: this.is_negotiable,
      registration_province: this.registration_province,
      registration_city: this.registration_city,
      establishment_date: this.establishment_date,
      registered_capital_range: this.registered_capital_range,
      paid_in_status: this.paid_in_status,
      company_type: this.company_type,
      tax_status: this.tax_status,
      bank_account_status: this.bank_account_status,
      has_trademark: this.has_trademark,
      has_patent: this.has_patent,
      has_software_copyright: this.has_software_copyright,
      has_license_plate: this.has_license_plate,
      has_social_security: this.has_social_security,
      shareholder_background: this.shareholder_background,
      has_bidding_history: this.has_bidding_history,
      description: this.description,
      expires_at: this.expires_at,
      created_at: this.created_at,
      updated_at: this.updated_at,
      publisher_nickname: this.publisher_nickname
    };
  }
}

module.exports = ListingPrisma;
