const { query, execute } = require('../config/db');

/**
 * 聊天群组模型
 */
class ChatGroup {
  constructor(data) {
    this.id = data.id;
    this.group_name = data.group_name;
    this.group_avatar = data.group_avatar;
    this.description = data.description;
    this.created_at = data.created_at;
  }

  /**
   * 创建新群组
   * @param {Object} groupData 群组数据
   * @returns {Promise<ChatGroup>}
   */
  static async create(groupData) {
    const { group_name, group_avatar, description } = groupData;

    const [result] = await execute(
      'INSERT INTO chat_groups (group_name, group_avatar, description) VALUES (?, ?, ?)',
      [group_name, group_avatar, description]
    );

    const rows = await query(
      'SELECT * FROM chat_groups WHERE id = ?',
      [result.insertId]
    );

    return new ChatGroup(rows[0]);
  }

  /**
   * 根据ID查找群组
   * @param {number} id 群组ID
   * @returns {Promise<ChatGroup|null>}
   */
  static async findById(id) {
    const rows = await query(
      'SELECT * FROM chat_groups WHERE id = ?',
      [id]
    );

    return rows.length > 0 ? new ChatGroup(rows[0]) : null;
  }

  /**
   * 获取所有群组列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const { page = 1, pageSize = 20 } = options;
    const offset = (page - 1) * pageSize;

    // 获取总数
    const countRows = await query(
      'SELECT COUNT(*) as total FROM chat_groups'
    );
    const total = countRows[0].total;

    // 获取群组列表 - 使用字符串拼接而不是参数绑定（LIMIT/OFFSET的限制）
    const rows = await query(
      `SELECT * FROM chat_groups ORDER BY id ASC LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}`
    );

    const groups = rows.map(row => new ChatGroup(row));

    return {
      data: groups,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 获取用户所在的群组列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array<ChatGroup>>}
   */
  static async getUserGroups(userId) {
    const rows = await query(`
      SELECT cg.* FROM chat_groups cg
      INNER JOIN group_members gm ON cg.id = gm.group_id
      WHERE gm.user_id = ?
      ORDER BY gm.joined_at DESC
    `, [userId]);

    return rows.map(row => new ChatGroup(row));
  }

  /**
   * 更新群组信息
   * @param {number} id 群组ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<ChatGroup|null>}
   */
  static async update(id, updateData) {
    const fields = [];
    const values = [];

    Object.keys(updateData).forEach(key => {
      if (['group_name', 'group_avatar', 'description'].includes(key)) {
        fields.push(`${key} = ?`);
        values.push(updateData[key]);
      }
    });

    if (fields.length === 0) {
      throw new Error('没有有效的更新字段');
    }

    values.push(id);

    await query(
      `UPDATE chat_groups SET ${fields.join(', ')} WHERE id = ?`,
      values
    );

    return await this.findById(id);
  }

  /**
   * 删除群组
   * @param {number} id 群组ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    const [result] = await execute(
      'DELETE FROM chat_groups WHERE id = ?',
      [id]
    );

    return result.affectedRows > 0;
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      group_name: this.group_name,
      group_avatar: this.group_avatar,
      description: this.description,
      created_at: this.created_at
    };
  }
}

module.exports = ChatGroup;
