const { prisma } = require('../config/prisma');

/**
 * 聊天群组模型 - Prisma版本
 */
class ChatGroupPrisma {
  constructor(data) {
    this.id = data.id;
    this.group_name = data.group_name;
    this.group_avatar = data.group_avatar;
    this.description = data.description;
    this.created_at = data.created_at;
    
    // 关联数据
    this.group_members = data.group_members;
    this.messages = data.messages;
  }

  /**
   * 创建新群组
   * @param {Object} groupData 群组数据
   * @returns {Promise<ChatGroupPrisma>}
   */
  static async create(groupData) {
    const { group_name, group_avatar, description } = groupData;

    const group = await prisma.chat_groups.create({
      data: {
        group_name,
        group_avatar: group_avatar || null,
        description: description || null
      }
    });

    return new ChatGroupPrisma(group);
  }

  /**
   * 根据ID查找群组
   * @param {number} id 群组ID
   * @returns {Promise<ChatGroupPrisma|null>}
   */
  static async findById(id) {
    const group = await prisma.chat_groups.findUnique({
      where: { id: parseInt(id) }
    });

    return group ? new ChatGroupPrisma(group) : null;
  }

  /**
   * 根据ID查找群组（包含成员信息）
   * @param {number} id 群组ID
   * @returns {Promise<ChatGroupPrisma|null>}
   */
  static async findByIdWithMembers(id) {
    const group = await prisma.chat_groups.findUnique({
      where: { id: BigInt(id) },
      include: {
        group_members: {
          include: {
            users: {
              select: {
                id: true,
                nickname: true,
                avatar_url: true
              }
            }
          }
        }
      }
    });

    return group ? new ChatGroupPrisma(group) : null;
  }

  /**
   * 获取所有群组列表
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getList(options = {}) {
    const { page = 1, pageSize = 20 } = options;
    const pageNum = parseInt(page);
    const pageSizeNum = parseInt(pageSize);
    const skip = (pageNum - 1) * pageSizeNum;

    // 并行查询总数和列表数据
    const [total, groups] = await Promise.all([
      prisma.chat_groups.count(),
      prisma.chat_groups.findMany({
        orderBy: { id: 'asc' },
        skip,
        take: pageSizeNum
      })
    ]);

    return {
      data: groups.map(group => new ChatGroupPrisma(group)),
      pagination: {
        page: pageNum,
        pageSize: pageSizeNum,
        total,
        totalPages: Math.ceil(total / pageSizeNum)
      }
    };
  }

  /**
   * 获取用户所在的群组列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array<ChatGroupPrisma>>}
   */
  static async getUserGroups(userId) {
    const groups = await prisma.chat_groups.findMany({
      where: {
        group_members: {
          some: {
            user_id: BigInt(userId)
          }
        }
      },
      include: {
        group_members: {
          where: {
            user_id: BigInt(userId)
          },
          select: {
            joined_at: true
          }
        }
      },
      orderBy: {
        group_members: {
          _count: 'desc'
        }
      }
    });

    return groups.map(group => new ChatGroupPrisma(group));
  }

  /**
   * 获取群组成员数量
   * @param {number} groupId 群组ID
   * @returns {Promise<number>}
   */
  static async getMemberCount(groupId) {
    return await prisma.group_members.count({
      where: { group_id: parseInt(groupId) }
    });
  }

  /**
   * 检查用户是否在群组中
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>}
   */
  static async isUserInGroup(groupId, userId) {
    const member = await prisma.group_members.findFirst({
      where: {
        group_id: parseInt(groupId),
        user_id: BigInt(userId)
      }
    });
    return !!member;
  }

  /**
   * 更新群组信息
   * @param {number} id 群组ID
   * @param {Object} updateData 更新数据
   * @returns {Promise<ChatGroupPrisma|null>}
   */
  static async update(id, updateData) {
    const allowedFields = ['group_name', 'group_avatar', 'description'];
    const data = {};

    Object.keys(updateData).forEach(key => {
      if (allowedFields.includes(key)) {
        data[key] = updateData[key];
      }
    });

    if (Object.keys(data).length === 0) {
      throw new Error('没有有效的更新字段');
    }

    const group = await prisma.chat_groups.update({
      where: { id: BigInt(id) },
      data
    });

    return new ChatGroupPrisma(group);
  }

  /**
   * 删除群组
   * @param {number} id 群组ID
   * @returns {Promise<boolean>}
   */
  static async delete(id) {
    await prisma.chat_groups.delete({
      where: { id: BigInt(id) }
    });
    return true;
  }

  /**
   * 获取群组统计信息
   * @returns {Promise<Object>}
   */
  static async getStats() {
    const [total, totalMembers, totalMessages] = await Promise.all([
      prisma.chat_groups.count(),
      prisma.group_members.count(),
      prisma.messages.count()
    ]);

    const avgMembersPerGroup = total > 0 ? Math.round(totalMembers / total) : 0;

    return {
      total,
      totalMembers,
      totalMessages,
      avgMembersPerGroup
    };
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id.toString(),
      group_name: this.group_name,
      group_avatar: this.group_avatar,
      description: this.description,
      created_at: this.created_at,
      // 如果有关联数据，也包含进来
      ...(this.group_members && { 
        member_count: this.group_members.length,
        members: this.group_members.map(member => ({
          user_id: member.user_id.toString(),
          joined_at: member.joined_at,
          ...(member.users && {
            nickname: member.users.nickname,
            avatar_url: member.users.avatar_url
          })
        }))
      })
    };
  }
}

module.exports = ChatGroupPrisma;
