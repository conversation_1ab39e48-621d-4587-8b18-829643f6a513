const { query, execute } = require('../config/db');

/**
 * 群组成员模型
 */
class GroupMember {
  constructor(data) {
    this.id = data.id;
    this.group_id = data.group_id;
    this.user_id = data.user_id;
    this.role = data.role;
    this.joined_at = data.joined_at;
  }

  /**
   * 添加成员到群组
   * @param {Object} memberData 成员数据
   * @returns {Promise<GroupMember>}
   */
  static async create(memberData) {
    const { group_id, user_id, role = 'member' } = memberData;
    
    // 检查是否已经是成员
    const existing = await this.findByGroupAndUser(group_id, user_id);
    if (existing) {
      throw new Error('用户已经是群组成员');
    }

    const [result] = await execute(
      'INSERT INTO group_members (group_id, user_id, role) VALUES (?, ?, ?)',
      [group_id, user_id, role]
    );

    const rows = await query(
      'SELECT * FROM group_members WHERE id = ?',
      [result.insertId]
    );

    return new GroupMember(rows[0]);
  }

  /**
   * 根据群组和用户查找成员关系
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @returns {Promise<GroupMember|null>}
   */
  static async findByGroupAndUser(groupId, userId) {
    const rows = await query(
      'SELECT * FROM group_members WHERE group_id = ? AND user_id = ?',
      [groupId, userId]
    );

    return rows.length > 0 ? new GroupMember(rows[0]) : null;
  }

  /**
   * 获取群组成员列表
   * @param {number} groupId 群组ID
   * @param {Object} options 查询选项
   * @returns {Promise<Object>}
   */
  static async getGroupMembers(groupId, options = {}) {
    const { page = 1, pageSize = 50 } = options;
    const offset = (page - 1) * pageSize;

    // 获取总数
    const countRows = await query(
      'SELECT COUNT(*) as total FROM group_members WHERE group_id = ?',
      [groupId]
    );
    const total = countRows[0].total;

    // 获取成员列表（包含用户信息）
    const rows = await query(`
      SELECT gm.*, u.nickname, u.avatar_url, u.phone_number
      FROM group_members gm
      LEFT JOIN users u ON gm.user_id = u.id
      WHERE gm.group_id = ?
      ORDER BY gm.joined_at DESC
      LIMIT ? OFFSET ?
    `, [groupId, pageSize, offset]);

    const members = rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      user: {
        nickname: row.nickname,
        avatar_url: row.avatar_url,
        phone_number: row.phone_number
      }
    }));

    return {
      data: members,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize)
      }
    };
  }

  /**
   * 获取用户的群组列表
   * @param {number} userId 用户ID
   * @returns {Promise<Array>}
   */
  static async getUserGroups(userId) {
    const rows = await query(`
      SELECT gm.*, cg.group_name, cg.group_avatar, cg.description
      FROM group_members gm
      LEFT JOIN chat_groups cg ON gm.group_id = cg.id
      WHERE gm.user_id = ?
      ORDER BY gm.joined_at DESC
    `, [userId]);

    return rows.map(row => ({
      id: row.id,
      group_id: row.group_id,
      user_id: row.user_id,
      role: row.role,
      joined_at: row.joined_at,
      group: {
        group_name: row.group_name,
        group_avatar: row.group_avatar,
        description: row.description
      }
    }));
  }

  /**
   * 更新成员角色
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @param {string} role 新角色
   * @returns {Promise<GroupMember|null>}
   */
  static async updateRole(groupId, userId, role) {
    const result = await query(
      'UPDATE group_members SET role = ? WHERE group_id = ? AND user_id = ?',
      [role, groupId, userId]
    );

    if (result.affectedRows === 0) {
      return null;
    }

    return await this.findByGroupAndUser(groupId, userId);
  }

  /**
   * 移除群组成员
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>}
   */
  static async remove(groupId, userId) {
    const result = await query(
      'DELETE FROM group_members WHERE group_id = ? AND user_id = ?',
      [groupId, userId]
    );

    return result.affectedRows > 0;
  }

  /**
   * 检查用户是否是群组成员
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>}
   */
  static async isMember(groupId, userId) {
    const member = await this.findByGroupAndUser(groupId, userId);
    return !!member;
  }

  /**
   * 检查用户是否是群组管理员或群主
   * @param {number} groupId 群组ID
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>}
   */
  static async isAdmin(groupId, userId) {
    const member = await this.findByGroupAndUser(groupId, userId);
    return member && ['admin', 'owner'].includes(member.role);
  }

  /**
   * 转换为JSON对象
   * @returns {Object}
   */
  toJSON() {
    return {
      id: this.id,
      group_id: this.group_id,
      user_id: this.user_id,
      role: this.role,
      joined_at: this.joined_at
    };
  }
}

module.exports = GroupMember;
