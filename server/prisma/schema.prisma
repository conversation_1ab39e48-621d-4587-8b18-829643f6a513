generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model administrators {
  id         Int      @id @default(autoincrement()) @db.UnsignedInt
  username   String   @unique(map: "uk_username") @db.VarChar(50)
  password   String   @db.VarChar(255)
  created_at DateTime @default(now()) @db.Timestamp(0)
  updated_at DateTime @default(now()) @db.Timestamp(0)
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model chat_groups {
  id            Int             @id @default(autoincrement()) @db.UnsignedInt
  group_name    String          @db.VarChar(255)
  group_avatar  String?         @db.VarChar(512)
  description   String?         @db.Text
  created_at    DateTime        @default(now()) @db.Timestamp(0)
  group_members group_members[]
  messages      messages[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model group_members {
  id          BigInt             @id @default(autoincrement()) @db.UnsignedBigInt
  group_id    Int                @db.UnsignedInt
  user_id     BigInt             @db.UnsignedBigInt
  role        group_members_role @default(member)
  joined_at   DateTime           @default(now()) @db.Timestamp(0)
  chat_groups chat_groups        @relation(fields: [group_id], references: [id], onDelete: Cascade, map: "fk_gm_group_id")
  users       users              @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "fk_gm_user_id")

  @@unique([group_id, user_id], map: "uk_group_user")
  @@index([user_id], map: "fk_gm_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model invitations {
  id                                       BigInt                   @id @default(autoincrement()) @db.UnsignedBigInt
  inviter_user_id                          BigInt                   @db.UnsignedBigInt
  invitee_user_id                          BigInt                   @unique(map: "uk_invitee_user_id") @db.UnsignedBigInt
  reward_type                              invitations_reward_type?
  reward_value                             Int?
  status                                   invitations_status       @default(unclaimed)
  created_at                               DateTime                 @default(now()) @db.Timestamp(0)
  users_invitations_invitee_user_idTousers users                    @relation("invitations_invitee_user_idTousers", fields: [invitee_user_id], references: [id], onDelete: Cascade, map: "fk_invitations_invitee_id")
  users_invitations_inviter_user_idTousers users                    @relation("invitations_inviter_user_idTousers", fields: [inviter_user_id], references: [id], onDelete: Cascade, map: "fk_invitations_inviter_id")

  @@index([inviter_user_id], map: "idx_inviter_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model listings {
  id                       BigInt                             @id @default(autoincrement()) @db.UnsignedBigInt
  user_id                  BigInt                             @db.UnsignedBigInt
  listing_type             listings_listing_type
  company_name             String                             @db.VarChar(255)
  status                   listings_status                    @default(dbgenerated("在售"))
  price                    Decimal?                           @db.Decimal(12, 2)
  is_negotiable            Boolean                            @default(false)
  registration_province    String?                            @db.VarChar(100)
  registration_city        String?                            @db.VarChar(100)
  establishment_date       DateTime?                          @db.Date
  registered_capital_range listings_registered_capital_range?
  paid_in_status           listings_paid_in_status?
  company_type             listings_company_type?
  tax_status               listings_tax_status?
  bank_account_status      listings_bank_account_status?
  has_trademark            Boolean                            @default(false)
  has_patent               Boolean                            @default(false)
  has_software_copyright   Boolean                            @default(false)
  has_license_plate        Boolean                            @default(false)
  has_social_security      Boolean                            @default(false)
  shareholder_background   listings_shareholder_background?
  has_bidding_history      Boolean                            @default(false)
  description              String?                            @db.Text
  expires_at               DateTime?                          @db.Timestamp(0)
  created_at               DateTime                           @default(now()) @db.Timestamp(0)
  updated_at               DateTime                           @default(now()) @db.Timestamp(0)
  users                    users                              @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "fk_listings_user_id")

  @@index([listing_type, status, registration_city], map: "idx_query")
  @@index([user_id], map: "idx_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model messages {
  id           BigInt                @id @default(autoincrement()) @db.UnsignedBigInt
  group_id     Int                   @db.UnsignedInt
  sender_id    BigInt                @db.UnsignedBigInt
  message_type messages_message_type @default(text)
  content      String                @db.Text
  created_at   DateTime              @default(now()) @db.Timestamp(0)
  chat_groups  chat_groups           @relation(fields: [group_id], references: [id], onDelete: Cascade, map: "fk_messages_group_id")
  users        users                 @relation(fields: [sender_id], references: [id], onDelete: Cascade, map: "fk_messages_sender_id")

  @@index([sender_id], map: "fk_messages_sender_id")
  @@index([group_id], map: "idx_group_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model orders {
  id             BigInt        @id @default(autoincrement()) @db.UnsignedBigInt
  order_number   String        @unique(map: "uk_order_number") @db.VarChar(64)
  user_id        BigInt        @db.UnsignedBigInt
  package_id     Int           @db.UnsignedInt
  amount         Decimal       @db.Decimal(10, 2)
  status         orders_status @default(pending)
  payment_method String?       @db.VarChar(50)
  transaction_id String?       @db.VarChar(255)
  paid_at        DateTime?     @db.Timestamp(0)
  created_at     DateTime      @default(now()) @db.Timestamp(0)
  updated_at     DateTime      @default(now()) @db.Timestamp(0)
  packages       packages      @relation(fields: [package_id], references: [id], map: "fk_orders_package_id")
  users          users         @relation(fields: [user_id], references: [id], onDelete: Cascade, map: "fk_orders_user_id")

  @@index([package_id], map: "fk_orders_package_id")
  @@index([status], map: "idx_status")
  @@index([user_id], map: "idx_user_id")
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model packages {
  id             Int                   @id @default(autoincrement()) @db.UnsignedInt
  title          String                @db.VarChar(255)
  description    String?               @db.VarChar(512)
  price          Decimal               @default(0.00) @db.Decimal(10, 2)
  credits_amount Int                   @db.UnsignedInt
  package_type   packages_package_type
  is_active      Boolean               @default(true)
  sort_order     Int                   @default(0)
  created_at     DateTime              @default(now()) @db.Timestamp(0)
  updated_at     DateTime              @default(now()) @db.Timestamp(0)
  orders         orders[]
}

/// This model or at least one of its fields has comments in the database, and requires an additional setup for migrations: Read more: https://pris.ly/d/database-comments
model users {
  id                                             BigInt          @id @default(autoincrement()) @db.UnsignedBigInt
  openid                                         String          @unique(map: "uk_openid") @db.VarChar(128)
  nickname                                       String?         @db.VarChar(255)
  avatar_url                                     String?         @db.VarChar(512)
  phone_number                                   String?         @unique(map: "uk_phone_number") @db.VarChar(20)
  status                                         users_status    @default(inactive)
  publishing_credits                             Int             @default(0) @db.UnsignedInt
  inviter_id                                     BigInt?         @db.UnsignedBigInt
  created_at                                     DateTime        @default(now()) @db.Timestamp(0)
  updated_at                                     DateTime        @default(now()) @db.Timestamp(0)
  group_members                                  group_members[]
  invitations_invitations_invitee_user_idTousers invitations?    @relation("invitations_invitee_user_idTousers")
  invitations_invitations_inviter_user_idTousers invitations[]   @relation("invitations_inviter_user_idTousers")
  listings                                       listings[]
  messages                                       messages[]
  orders                                         orders[]
  users                                          users?          @relation("usersTousers", fields: [inviter_id], references: [id], map: "fk_users_inviter_id")
  other_users                                    users[]         @relation("usersTousers")

  @@index([inviter_id], map: "idx_inviter_id")
}

enum listings_listing_type {
  COMPANY @map("公司")
  INDIVIDUAL @map("个体户")
  ACCOUNTING @map("代账户")
}

enum messages_message_type {
  text
  image
  system
  demand_card
}

enum group_members_role {
  member
  admin
  owner
}

enum invitations_reward_type {
  activation
  credits
}

enum listings_status {
  ON_SALE @map("在售")
  SOLD @map("已售")
  OFF_SHELF @map("下架")
}

enum users_status {
  inactive
  active
}

enum invitations_status {
  claimed
  unclaimed
}

enum packages_package_type {
  free
  paid
  promo
}

enum orders_status {
  pending
  paid
  failed
  completed
  canceled
}

enum listings_registered_capital_range {
  UNDER_100K @map("10万以下")
  FROM_100K_TO_500K @map("10-50万")
  FROM_500K_TO_1M @map("50-100万")
  FROM_1M_TO_5M @map("100-500万")
  FROM_5M_TO_10M @map("500-1000万")
  OVER_10M @map("1000万以上")
}

enum listings_paid_in_status {
  PAID @map("已实缴")
  UNPAID @map("未实缴")
  UNCERTAIN @map("不确定")
}

enum listings_company_type {
  NORMAL @map("普通公司")
  NATIONAL @map("国家局公司")
  LISTED @map("上市公司")
  UNCERTAIN @map("不确定")
}

enum listings_tax_status {
  UNREGISTERED @map("未登记")
  SMALL_SCALE @map("小规模")
  GENERAL_TAXPAYER @map("一般纳税人")
  NOT_OPENED @map("未开业")
  UNCERTAIN @map("不确定")
}

enum listings_bank_account_status {
  OPENED @map("已开户")
  NOT_OPENED @map("未开户")
  UNCERTAIN @map("不确定")
}

enum listings_shareholder_background {
  NATURAL_PERSON @map("自然人")
  STATE_OWNED @map("国央企")
  FOREIGN @map("外资")
  UNCERTAIN @map("不确定")
}
