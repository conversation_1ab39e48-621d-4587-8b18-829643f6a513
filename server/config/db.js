const mysql = require('mysql2/promise');
require('dotenv').config();

/**
 * 数据库连接池配置
 * 使用连接池可以提高数据库连接的效率和性能
 */
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || 'root123',
  database: process.env.DB_NAME || 'shuaiDanSystem',
  waitForConnections: true,
  connectionLimit: 10, // 最大连接数
  queueLimit: 0,
  charset: 'utf8mb4' // 支持emoji和中文
});

/**
 * 测试数据库连接
 */
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

/**
 * 执行SQL查询的封装函数
 * @param {string} sql - SQL语句
 * @param {Array} params - 参数数组
 * @returns {Promise} 查询结果
 */
async function query(sql, params = []) {
  try {
    const [rows, fields] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('SQL查询错误:', error.message);
    throw error;
  }
}

/**
 * 执行SQL查询并返回完整结果（包括insertId等）
 * @param {string} sql - SQL语句
 * @param {Array} params - 参数数组
 * @returns {Promise} 完整查询结果
 */
async function execute(sql, params = []) {
  try {
    return await pool.execute(sql, params);
  } catch (error) {
    console.error('SQL执行错误:', error.message);
    throw error;
  }
}

/**
 * 开始事务
 */
async function beginTransaction() {
  const connection = await pool.getConnection();
  await connection.beginTransaction();
  return connection;
}

/**
 * 提交事务
 */
async function commitTransaction(connection) {
  await connection.commit();
  connection.release();
}

/**
 * 回滚事务
 */
async function rollbackTransaction(connection) {
  await connection.rollback();
  connection.release();
}

module.exports = {
  pool,
  query,
  execute,
  testConnection,
  beginTransaction,
  commitTransaction,
  rollbackTransaction
};
