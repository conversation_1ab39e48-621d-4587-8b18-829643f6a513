const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3000/api/v1';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
});

/**
 * 聊天API测试脚本
 */
class ChatAPITester {
  constructor() {
    this.token = null;
    this.userId = null;
    this.groupId = null;
  }

  /**
   * 测试健康检查
   */
  async testHealth() {
    try {
      console.log('🔍 测试健康检查...');
      const response = await axios.get('http://localhost:3000/health');
      console.log('✅ 健康检查通过:', response.data);
      return true;
    } catch (error) {
      console.error('❌ 健康检查失败:', error.message);
      return false;
    }
  }

  /**
   * 测试登录（模拟）
   */
  async testLogin() {
    try {
      console.log('🔍 测试模拟登录...');
      
      // 模拟登录请求
      const response = await api.post('/auth/login', {
        code: 'mock_wx_code_for_testing'
      });
      
      if (response.data.success) {
        this.token = response.data.data.token;
        this.userId = response.data.data.user.id;
        console.log('✅ 模拟登录成功, 用户ID:', this.userId);
        
        // 设置认证头
        api.defaults.headers.common['Authorization'] = `Bearer ${this.token}`;
        return true;
      } else {
        console.error('❌ 模拟登录失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.log('⚠️ 模拟登录失败（可能是微信API配置问题）:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试获取群组列表
   */
  async testGetGroups() {
    try {
      console.log('🔍 测试获取群组列表...');
      const response = await api.get('/chat/groups');
      
      if (response.data.success) {
        console.log('✅ 获取群组列表成功, 群组数量:', response.data.data.data.length);
        if (response.data.data.data.length > 0) {
          this.groupId = response.data.data.data[0].id;
          console.log('📝 使用群组ID:', this.groupId);
        }
        return true;
      } else {
        console.error('❌ 获取群组列表失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取群组列表失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试加入群组
   */
  async testJoinGroup() {
    if (!this.token || !this.groupId) {
      console.log('⚠️ 跳过加入群组测试（无token或群组ID）');
      return false;
    }

    try {
      console.log('🔍 测试加入群组...');
      const response = await api.post(`/chat/groups/${this.groupId}/join`);
      
      if (response.data.success) {
        console.log('✅ 加入群组成功');
        return true;
      } else {
        console.log('⚠️ 加入群组失败（可能已经是成员）:', response.data.message);
        return true; // 已经是成员也算成功
      }
    } catch (error) {
      console.log('⚠️ 加入群组失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试发送消息
   */
  async testSendMessage() {
    if (!this.token || !this.groupId) {
      console.log('⚠️ 跳过发送消息测试（无token或群组ID）');
      return false;
    }

    try {
      console.log('🔍 测试发送消息...');
      const response = await api.post(`/chat/groups/${this.groupId}/messages`, {
        content: '这是一条测试消息 - ' + new Date().toLocaleString(),
        message_type: 'text'
      });
      
      if (response.data.success) {
        console.log('✅ 发送消息成功, 消息ID:', response.data.data.id);
        return true;
      } else {
        console.error('❌ 发送消息失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 发送消息失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试获取消息列表
   */
  async testGetMessages() {
    if (!this.token || !this.groupId) {
      console.log('⚠️ 跳过获取消息测试（无token或群组ID）');
      return false;
    }

    try {
      console.log('🔍 测试获取消息列表...');
      const response = await api.get(`/chat/groups/${this.groupId}/messages`);
      
      if (response.data.success) {
        console.log('✅ 获取消息列表成功, 消息数量:', response.data.data.data.length);
        return true;
      } else {
        console.error('❌ 获取消息列表失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取消息列表失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 测试获取新消息（轮询）
   */
  async testGetNewMessages() {
    if (!this.token || !this.groupId) {
      console.log('⚠️ 跳过获取新消息测试（无token或群组ID）');
      return false;
    }

    try {
      console.log('🔍 测试获取新消息（轮询）...');
      const response = await api.get(`/chat/groups/${this.groupId}/new-messages?lastMessageId=0`);
      
      if (response.data.success) {
        console.log('✅ 获取新消息成功, 新消息数量:', response.data.data.length);
        return true;
      } else {
        console.error('❌ 获取新消息失败:', response.data.message);
        return false;
      }
    } catch (error) {
      console.error('❌ 获取新消息失败:', error.response?.data?.message || error.message);
      return false;
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始聊天API测试...\n');

    const tests = [
      { name: '健康检查', fn: () => this.testHealth() },
      { name: '模拟登录', fn: () => this.testLogin() },
      { name: '获取群组列表', fn: () => this.testGetGroups() },
      { name: '加入群组', fn: () => this.testJoinGroup() },
      { name: '发送消息', fn: () => this.testSendMessage() },
      { name: '获取消息列表', fn: () => this.testGetMessages() },
      { name: '获取新消息', fn: () => this.testGetNewMessages() }
    ];

    let passed = 0;
    let total = tests.length;

    for (const test of tests) {
      try {
        const result = await test.fn();
        if (result) passed++;
      } catch (error) {
        console.error(`❌ ${test.name} 测试异常:`, error.message);
      }
      console.log(''); // 空行分隔
    }

    console.log(`📊 测试完成: ${passed}/${total} 通过`);
    
    if (passed === total) {
      console.log('🎉 所有测试通过！');
    } else {
      console.log('⚠️ 部分测试失败，请检查服务器状态和数据库连接');
    }
  }
}

// 运行测试
const tester = new ChatAPITester();
tester.runAllTests().catch(console.error);
