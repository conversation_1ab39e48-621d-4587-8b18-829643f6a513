const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3000/api/v1';

/**
 * 简单的API测试
 */
async function testAPI() {
  console.log('🚀 开始API测试...\n');

  try {
    // 1. 测试健康检查
    console.log('🔍 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ 健康检查通过:', healthResponse.data.status);

    // 2. 测试获取群组列表（不需要认证）
    console.log('\n🔍 测试获取群组列表...');
    const groupsResponse = await axios.get(`${BASE_URL}/chat/groups`);
    
    if (groupsResponse.data.success) {
      console.log('✅ 获取群组列表成功');
      console.log(`📊 群组数量: ${groupsResponse.data.data.data.length}`);
      
      // 显示群组信息
      groupsResponse.data.data.data.forEach((group, index) => {
        console.log(`   ${index + 1}. ${group.group_name} (ID: ${group.id})`);
      });

      // 3. 测试获取第一个群组的消息（不需要认证）
      if (groupsResponse.data.data.data.length > 0) {
        const firstGroupId = groupsResponse.data.data.data[0].id;
        console.log(`\n🔍 测试获取群组 ${firstGroupId} 的消息列表...`);
        
        const messagesResponse = await axios.get(`${BASE_URL}/chat/groups/${firstGroupId}/messages`);
        
        if (messagesResponse.data.success) {
          console.log('✅ 获取消息列表成功');
          console.log(`📊 消息数量: ${messagesResponse.data.data.data.length}`);
          
          // 显示前3条消息
          const messages = messagesResponse.data.data.data.slice(0, 3);
          messages.forEach((message, index) => {
            const senderName = message.sender?.nickname || '系统';
            const content = message.content.length > 50 ? 
              message.content.substring(0, 50) + '...' : 
              message.content;
            console.log(`   ${index + 1}. [${senderName}] ${content}`);
          });

          // 4. 测试获取新消息（不需要认证）
          console.log(`\n🔍 测试获取群组 ${firstGroupId} 的新消息...`);
          const newMessagesResponse = await axios.get(`${BASE_URL}/chat/groups/${firstGroupId}/new-messages?lastMessageId=0`);
          
          if (newMessagesResponse.data.success) {
            console.log('✅ 获取新消息成功');
            console.log(`📊 新消息数量: ${newMessagesResponse.data.data.length}`);
          } else {
            console.error('❌ 获取新消息失败:', newMessagesResponse.data.message);
          }

        } else {
          console.error('❌ 获取消息列表失败:', messagesResponse.data.message);
        }
      }
      
      console.log('\n🎉 基础API测试完成！');
      console.log('\n📝 说明:');
      console.log('   - 群组列表和消息获取功能正常');
      console.log('   - 未登录用户可以查看群组和消息');
      console.log('   - 发送消息仍需要用户认证');
      
    } else {
      console.error('❌ 获取群组列表失败:', groupsResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data?.message || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保服务器正在运行');
      console.log('   启动命令: npm run dev');
    }
  }
}

// 运行测试
testAPI();
