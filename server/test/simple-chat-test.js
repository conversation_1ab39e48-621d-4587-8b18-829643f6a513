const axios = require('axios');

// 配置基础URL
const BASE_URL = 'http://localhost:3000/api/v1';

/**
 * 简单的聊天API测试（不需要认证的部分）
 */
async function testChatAPI() {
  console.log('🚀 开始简单聊天API测试...\n');

  try {
    // 1. 测试健康检查
    console.log('🔍 测试健康检查...');
    const healthResponse = await axios.get('http://localhost:3000/health');
    console.log('✅ 健康检查通过:', healthResponse.data.status);

    // 2. 测试获取群组列表（不需要认证）
    console.log('\n🔍 测试获取群组列表...');
    const groupsResponse = await axios.get(`${BASE_URL}/chat/groups`);
    
    if (groupsResponse.data.success) {
      console.log('✅ 获取群组列表成功');
      console.log(`📊 群组数量: ${groupsResponse.data.data.data.length}`);
      
      // 显示群组信息
      groupsResponse.data.data.data.forEach((group, index) => {
        console.log(`   ${index + 1}. ${group.group_name} (ID: ${group.id})`);
      });
      
      // 3. 测试获取群组详情
      if (groupsResponse.data.data.data.length > 0) {
        const firstGroup = groupsResponse.data.data.data[0];
        console.log(`\n🔍 测试获取群组详情 (${firstGroup.group_name})...`);
        
        try {
          const groupDetailResponse = await axios.get(`${BASE_URL}/chat/groups/${firstGroup.id}`);
          if (groupDetailResponse.data.success) {
            console.log('✅ 获取群组详情成功');
            console.log(`   群组名称: ${groupDetailResponse.data.data.group_name}`);
            console.log(`   群组描述: ${groupDetailResponse.data.data.description || '无'}`);
          }
        } catch (error) {
          console.log('⚠️ 获取群组详情失败:', error.response?.data?.message || error.message);
        }

        // 4. 测试获取群组消息（不需要认证）
        console.log(`\n🔍 测试获取群组消息 (${firstGroup.group_name})...`);
        try {
          const messagesResponse = await axios.get(`${BASE_URL}/chat/groups/${firstGroup.id}/messages`);
          if (messagesResponse.data.success) {
            console.log('✅ 获取群组消息成功');
            console.log(`📊 消息数量: ${messagesResponse.data.data.data.length}`);
            
            // 显示最近几条消息
            const messages = messagesResponse.data.data.data.slice(-3);
            messages.forEach((message, index) => {
              const senderName = message.sender?.nickname || '系统';
              const content = message.content.length > 30 
                ? message.content.substring(0, 30) + '...' 
                : message.content;
              console.log(`   ${index + 1}. [${senderName}] ${content}`);
            });
          }
        } catch (error) {
          console.log('⚠️ 获取群组消息失败:', error.response?.data?.message || error.message);
        }
      }
      
      console.log('\n🎉 基础聊天API测试完成！');
      console.log('\n📝 说明:');
      console.log('   - 群组列表和消息获取功能正常');
      console.log('   - 发送消息和加入群组需要用户认证');
      console.log('   - 小程序端可以正常显示群组和消息');
      
    } else {
      console.error('❌ 获取群组列表失败:', groupsResponse.data.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data?.message || error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 提示: 请确保服务器正在运行');
      console.log('   启动命令: npm run dev');
    }
  }
}

// 运行测试
testChatAPI();
