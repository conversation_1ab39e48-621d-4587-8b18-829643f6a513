const express = require('express');
const router = express.Router();

const ListingPrisma = require('../models/ListingPrisma');
const UserPrisma = require('../models/UserPrisma');
const { authenticateToken, requireActive, requireCredits, optionalAuth } = require('../middleware/auth');
const ResponseHelper = require('../utils/response');
const Validator = require('../utils/validator');

/**
 * 接口 4: 发布新信息
 * POST /api/v1/listings
 * 
 * 逻辑: 使用 JWT 中间件。接收小程序表单提交的所有公司信息。校验数据合法性
 * 数据库: 在 listings 表中插入一条新数据，user_id 从 JWT 中获取
 * 输出: 成功创建的消息
 */
router.post('/', authenticateToken, requireActive, requireCredits, async (req, res) => {
  try {
    const userId = req.user.id;
    const listingData = { ...req.body, user_id: userId };

    // 验证数据
    const errors = Validator.validateListingData(listingData);
    if (errors.length > 0) {
      return ResponseHelper.validationError(res, errors);
    }

    // 创建挂牌信息
    const listing = await ListingPrisma.create(listingData);

    // 减少用户发布额度
    await UserPrisma.reduceCredits(userId, 1);

    ResponseHelper.success(res, listing.toJSON(), '发布成功', 201);

  } catch (error) {
    console.error('发布信息错误:', error);

    if (error.message === '发布额度不足') {
      return ResponseHelper.forbidden(res, '发布额度不足，请购买套餐');
    }

    ResponseHelper.serverError(res, '发布失败', error);
  }
});

/**
 * 接口 5: 获取信息列表
 * GET /api/v1/listings
 * 
 * 逻辑: 无需登录即可访问。实现分页功能 (输入 page, pageSize)。按创建时间降序排序
 * 输出: 分页后的信息列表及总条数
 */
router.get('/', optionalAuth, async (req, res) => {
  try {
    // 验证分页参数
    const { page, pageSize } = Validator.validatePagination(req.query);
    
    // 获取筛选参数
    const {
      status = '在售',
      listing_type,
      registration_city,
      company_type,
      tax_status,
      search
    } = req.query;

    const options = {
      page,
      pageSize,
      status,
      listing_type,
      registration_city,
      company_type,
      tax_status,
      search
    };

    // 获取信息列表
    const result = await ListingPrisma.getList(options);

    ResponseHelper.paginated(
      res,
      result.data.map(listing => listing.toJSON()),
      result.pagination,
      '获取信息列表成功'
    );

  } catch (error) {
    console.error('获取信息列表错误:', error);
    ResponseHelper.serverError(res, '获取信息列表失败', error);
  }
});

/**
 * 接口 6: 获取信息详情
 * GET /api/v1/listings/:id
 * 
 * 逻辑: 根据传入的 id，查询 listings 表，并关联查询发布者的部分信息（如昵称）
 * 输出: 完整的单条信息详情
 */
router.get('/:id', optionalAuth, async (req, res) => {
  try {
    const { id } = req.params;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    // 获取信息详情
    const listing = await ListingPrisma.findById(parseInt(id));
    
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    ResponseHelper.success(res, listing.toJSON(), '获取信息详情成功');

  } catch (error) {
    console.error('获取信息详情错误:', error);
    ResponseHelper.serverError(res, '获取信息详情失败', error);
  }
});

/**
 * 更新挂牌信息
 * PUT /api/v1/listings/:id
 */
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    // 查找信息
    const listing = await ListingPrisma.findById(parseInt(id));
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 检查权限（只能修改自己发布的信息）
    if (listing.user_id !== userId) {
      return ResponseHelper.forbidden(res, '只能修改自己发布的信息');
    }

    // 验证更新数据
    const errors = Validator.validateListingData(req.body, true);
    if (errors.length > 0) {
      return ResponseHelper.validationError(res, errors);
    }

    // 更新信息
    const updatedListing = await ListingPrisma.update(parseInt(id), req.body);

    ResponseHelper.success(res, updatedListing.toJSON(), '更新成功');

  } catch (error) {
    console.error('更新信息错误:', error);
    ResponseHelper.serverError(res, '更新失败', error);
  }
});

/**
 * 删除挂牌信息
 * DELETE /api/v1/listings/:id
 */
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 验证ID格式
    if (!id || isNaN(parseInt(id))) {
      return ResponseHelper.validationError(res, ['无效的信息ID']);
    }

    // 查找信息
    const listing = await ListingPrisma.findById(parseInt(id));
    if (!listing) {
      return ResponseHelper.notFound(res, '信息不存在');
    }

    // 检查权限（只能删除自己发布的信息）
    if (listing.user_id !== userId) {
      return ResponseHelper.forbidden(res, '只能删除自己发布的信息');
    }

    // 删除信息
    const deleted = await ListingPrisma.delete(parseInt(id));
    
    if (deleted) {
      ResponseHelper.success(res, null, '删除成功');
    } else {
      ResponseHelper.error(res, '删除失败', 500);
    }

  } catch (error) {
    console.error('删除信息错误:', error);
    ResponseHelper.serverError(res, '删除失败', error);
  }
});

/**
 * 获取挂牌信息统计
 * GET /api/v1/listings/stats
 */
router.get('/stats', async (req, res) => {
  try {
    const stats = await ListingPrisma.getStats();
    ResponseHelper.success(res, stats, '获取统计信息成功');
  } catch (error) {
    console.error('获取统计信息错误:', error);
    ResponseHelper.serverError(res, '获取统计信息失败', error);
  }
});

module.exports = router;
