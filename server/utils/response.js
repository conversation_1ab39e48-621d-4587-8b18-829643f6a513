/**
 * 统一响应格式工具类
 */
class ResponseHelper {
  /**
   * 成功响应
   * @param {Object} res Express响应对象
   * @param {*} data 响应数据
   * @param {string} message 响应消息
   * @param {number} code HTTP状态码
   */
  static success(res, data = null, message = '操作成功', code = 200) {
    return res.status(code).json({
      success: true,
      message,
      data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 错误响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   * @param {number} code HTTP状态码
   * @param {*} error 错误详情（开发环境使用）
   */
  static error(res, message = '操作失败', code = 400, error = null) {
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    // 开发环境返回错误详情
    if (process.env.NODE_ENV === 'development' && error) {
      response.error = error;
    }

    return res.status(code).json(response);
  }

  /**
   * 分页响应
   * @param {Object} res Express响应对象
   * @param {Array} data 数据列表
   * @param {Object} pagination 分页信息
   * @param {string} message 响应消息
   */
  static paginated(res, data, pagination, message = '获取成功') {
    return res.status(200).json({
      success: true,
      message,
      data,
      pagination,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 验证错误响应
   * @param {Object} res Express响应对象
   * @param {Array|Object} errors 验证错误信息
   */
  static validationError(res, errors) {
    return res.status(422).json({
      success: false,
      message: '数据验证失败',
      errors,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 未授权响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static unauthorized(res, message = '未授权访问') {
    return res.status(401).json({
      success: false,
      message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 禁止访问响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static forbidden(res, message = '禁止访问') {
    return res.status(403).json({
      success: false,
      message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 资源未找到响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   */
  static notFound(res, message = '资源未找到') {
    return res.status(404).json({
      success: false,
      message,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * 服务器内部错误响应
   * @param {Object} res Express响应对象
   * @param {string} message 错误消息
   * @param {*} error 错误详情
   */
  static serverError(res, message = '服务器内部错误', error = null) {
    console.error('服务器错误:', error);
    
    const response = {
      success: false,
      message,
      timestamp: new Date().toISOString()
    };

    // 开发环境返回错误详情
    if (process.env.NODE_ENV === 'development' && error) {
      response.error = error.message || error;
      response.stack = error.stack;
    }

    return res.status(500).json(response);
  }
}

module.exports = ResponseHelper;
