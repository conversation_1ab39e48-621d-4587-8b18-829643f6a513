<template>
  <view class="invite-container">
    <!-- 邀请信息卡片 -->
    <view class="invite-card">
      <view class="invite-header">
        <text class="invite-title">邀请好友注册</text>
        <text class="invite-subtitle">每成功邀请1位好友，您将获得1条发布条数</text>
      </view>
      
      <view class="invite-stats">
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.totalInvites || 0 }}</text>
          <text class="stat-label">累计邀请</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.successInvites || 0 }}</text>
          <text class="stat-label">成功邀请</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ inviteStats.earnedCredits || 0 }}</text>
          <text class="stat-label">获得条数</text>
        </view>
      </view>
    </view>

    <!-- 分享方式 -->
    <view class="share-methods">
      <view class="method-title">选择分享方式</view>
      
      <view class="method-grid">
        <view class="method-item" @click="shareToFriend">
          <view class="method-icon">👥</view>
          <text class="method-text">分享给好友</text>
        </view>
        
        <view class="method-item" @click="shareToGroup">
          <view class="method-icon">💬</view>
          <text class="method-text">分享到群聊</text>
        </view>
        
        <view class="method-item" @click="copyInviteLink">
          <view class="method-icon">🔗</view>
          <text class="method-text">复制链接</text>
        </view>
        
        <view class="method-item" @click="saveQRCode">
          <view class="method-icon">📱</view>
          <text class="method-text">保存二维码</text>
        </view>
      </view>
    </view>

    <!-- 邀请记录 -->
    <view class="invite-records">
      <view class="records-header">
        <text class="records-title">邀请记录</text>
        <text class="records-count">共{{ inviteRecords.length }}条</text>
      </view>
      
      <view v-if="inviteRecords.length === 0" class="empty-records">
        <text class="empty-text">暂无邀请记录</text>
      </view>
      
      <view v-else class="records-list">
        <view v-for="record in inviteRecords" :key="record.id" class="record-item">
          <view class="record-info">
            <text class="record-name">{{ record.invitee_nickname || '新用户' }}</text>
            <text class="record-time">{{ formatTime(record.created_at) }}</text>
          </view>
          <view class="record-status" :class="getStatusClass(record.status)">
            {{ getStatusText(record.status) }}
          </view>
        </view>
      </view>
    </view>

    <!-- 邀请规则 -->
    <view class="invite-rules">
      <view class="rules-title">邀请规则</view>
      <view class="rules-list">
        <text class="rule-item">• 每成功邀请1位新用户注册，您将获得1条发布条数</text>
        <text class="rule-item">• 被邀请用户必须是首次注册的新用户</text>
        <text class="rule-item">• 邀请奖励将在被邀请用户完成注册后立即发放</text>
        <text class="rule-item">• 发布条数永久有效，无使用期限</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { userAPI, utils } from '@/utils/api.js'

// 响应式数据
const inviteStats = ref({})
const inviteRecords = ref([])
const isLoading = ref(false)

// 生命周期
onMounted(() => {
  loadInviteData()
})

// 方法定义
const loadInviteData = async () => {
  try {
    isLoading.value = true
    
    // 加载邀请统计
    const statsResponse = await userAPI.getInviteStats()
    if (statsResponse.success) {
      inviteStats.value = statsResponse.data
    }
    
    // 加载邀请记录
    const recordsResponse = await userAPI.getInviteRecords()
    if (recordsResponse.success) {
      inviteRecords.value = recordsResponse.data.items || []
    }
    
  } catch (error) {
    utils.handleError(error, '加载邀请数据失败')
  } finally {
    isLoading.value = false
  }
}

const shareToFriend = () => {
  const inviteUrl = generateInviteUrl()
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: inviteUrl,
    title: '甩单系统邀请',
    summary: '快来加入甩单系统，发现更多商机！',
    imageUrl: '/static/share-logo.png',
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      // 降级到复制链接
      copyInviteLink()
    }
  })
}

const shareToGroup = () => {
  const inviteUrl = generateInviteUrl()
  
  uni.share({
    provider: 'weixin',
    scene: 'WXSceneTimeline',
    type: 0,
    href: inviteUrl,
    title: '甩单系统邀请',
    summary: '快来加入甩单系统，发现更多商机！',
    imageUrl: '/static/share-logo.png',
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      // 降级到复制链接
      copyInviteLink()
    }
  })
}

const copyInviteLink = () => {
  const inviteUrl = generateInviteUrl()
  
  uni.setClipboardData({
    data: inviteUrl,
    success: () => {
      uni.showToast({
        title: '链接已复制',
        icon: 'success'
      })
    }
  })
}

const saveQRCode = () => {
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

const generateInviteUrl = () => {
  // 获取当前用户ID作为邀请者ID
  const userInfo = uni.getStorageSync('userInfo')
  const inviterId = userInfo?.id || ''
  
  // 生成邀请链接（实际应用中应该是小程序的分享链接）
  return `https://your-miniprogram.com/pages/login/login?inviterId=${inviterId}`
}

const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  return `${date.getMonth() + 1}月${date.getDate()}日`
}

const getStatusClass = (status) => {
  switch (status) {
    case 'completed':
      return 'status-success'
    case 'pending':
      return 'status-pending'
    default:
      return 'status-failed'
  }
}

const getStatusText = (status) => {
  switch (status) {
    case 'completed':
      return '已完成'
    case 'pending':
      return '待完成'
    default:
      return '已失效'
  }
}
</script>

<style scoped>
.invite-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.invite-card {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.invite-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.invite-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}

.invite-subtitle {
  font-size: 28rpx;
  color: #666;
}

.invite-stats {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #22c55e;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.share-methods {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.method-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.method-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.method-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30rpx;
  border: 1rpx solid #eee;
  border-radius: 12rpx;
  background: #f9f9f9;
}

.method-item:active {
  background: #f0f0f0;
}

.method-icon {
  font-size: 48rpx;
  margin-bottom: 15rpx;
}

.method-text {
  font-size: 28rpx;
  color: #333;
}

.invite-records {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.records-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.records-count {
  font-size: 26rpx;
  color: #666;
}

.empty-records {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.records-list {
  /* 记录列表样式 */
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #f5f5f5;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  flex: 1;
}

.record-name {
  display: block;
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
}

.status-success {
  background: #22c55e;
}

.status-pending {
  background: #f59e0b;
}

.status-failed {
  background: #ef4444;
}

.invite-rules {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.rules-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.rules-list {
  /* 规则列表样式 */
}

.rule-item {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15rpx;
}

.rule-item:last-child {
  margin-bottom: 0;
}
</style>
