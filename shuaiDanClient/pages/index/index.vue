<template>
	<view class="container">
		<!-- 头部区域 -->
		<view class="header">
			<image class="logo" src="/static/logo.png"></image>
			<text class="app-title">甩单系统</text>
			<text class="app-subtitle">专业的公司转让平台</text>
		</view>

		<!-- 功能导航 -->
		<view class="nav-section">
			<view class="nav-grid">
				<view class="nav-item" @click="goToListings">
					<view class="nav-icon">🏢</view>
					<text class="nav-title">公司转让</text>
					<text class="nav-desc">浏览转让信息</text>
				</view>

				<view class="nav-item" @click="goToPublish">
					<view class="nav-icon">📝</view>
					<text class="nav-title">发布信息</text>
					<text class="nav-desc">发布转让信息</text>
				</view>

				<view class="nav-item" @click="goToProfile">
					<view class="nav-icon">👤</view>
					<text class="nav-title">个人中心</text>
					<text class="nav-desc">管理我的信息</text>
				</view>

				<view class="nav-item" @click="goToChat">
					<view class="nav-icon">💬</view>
					<text class="nav-title">交流群</text>
					<text class="nav-desc">加入交流群</text>
				</view>
			</view>
		</view>

		<!-- 快捷操作 -->
		<view class="quick-actions">
			<view class="action-card" @click="goToListings">
				<view class="card-header">
					<text class="card-title">最新转让信息</text>
					<text class="card-more">查看更多 ></text>
				</view>
				<text class="card-desc">发现优质公司转让机会</text>
			</view>

			<view class="action-card" @click="goToPublish">
				<view class="card-header">
					<text class="card-title">快速发布</text>
					<text class="card-more">立即发布 ></text>
				</view>
				<text class="card-desc">快速发布您的转让信息</text>
			</view>
		</view>

		<!-- 统计信息 -->
		<view class="stats-section">
			<view class="stats-grid">
				<view class="stat-item">
					<text class="stat-number">1000+</text>
					<text class="stat-label">成功案例</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">5000+</text>
					<text class="stat-label">注册用户</text>
				</view>
				<view class="stat-item">
					<text class="stat-number">100+</text>
					<text class="stat-label">每日新增</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { onMounted } from 'vue'
import { utils } from '@/utils/api.js'

// 响应式数据
// const title = ref('甩单系统') // 暂时不需要

// 生命周期钩子
onMounted(() => {
	// 检查登录状态
	checkLoginStatus()
})

// 方法定义
const checkLoginStatus = () => {
	if (!utils.isLoggedIn()) {
		// 如果未登录，跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})
	}
}

const goToListings = () => {
	uni.navigateTo({
		url: '/pages/listings/list'
	})
}

const goToPublish = () => {
	uni.navigateTo({
		url: '/pages/listings/publish'
	})
}

const goToProfile = () => {
	uni.navigateTo({
		url: '/pages/user/profile'
	})
}

const goToChat = () => {
	uni.navigateTo({
		url: '/pages/chat/chat'
	})
}
</script>

<style scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
	padding: 40rpx 30rpx;
}

.header {
	text-align: center;
	margin-bottom: 60rpx;
	padding-top: 60rpx;
}

.logo {
	width: 160rpx;
	height: 160rpx;
	border-radius: 80rpx;
	margin-bottom: 30rpx;
	background: white;
	padding: 20rpx;
}

.app-title {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 15rpx;
}

.app-subtitle {
	display: block;
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.8);
}

.nav-section {
	margin-bottom: 40rpx;
}

.nav-grid {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 20rpx;
}

.nav-item {
	background: white;
	border-radius: 16rpx;
	padding: 40rpx 30rpx;
	text-align: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.nav-icon {
	font-size: 60rpx;
	margin-bottom: 20rpx;
}

.nav-title {
	display: block;
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	margin-bottom: 10rpx;
}

.nav-desc {
	font-size: 24rpx;
	color: #666;
}

.quick-actions {
	margin-bottom: 40rpx;
}

.action-card {
	background: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 15rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.card-more {
	font-size: 26rpx;
	color: #22c55e;
}

.card-desc {
	font-size: 26rpx;
	color: #666;
}

.stats-section {
	background: rgba(255, 255, 255, 0.1);
	border-radius: 16rpx;
	padding: 40rpx;
	backdrop-filter: blur(10rpx);
}

.stats-grid {
	display: flex;
	justify-content: space-around;
}

.stat-item {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 48rpx;
	font-weight: bold;
	color: white;
	margin-bottom: 10rpx;
}

.stat-label {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
}

.nav-item:active,
.action-card:active {
	transform: scale(0.98);
	transition: transform 0.1s;
}
</style>
