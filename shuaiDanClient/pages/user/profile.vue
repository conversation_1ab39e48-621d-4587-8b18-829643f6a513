<template>
  <view class="profile-container">
    <!-- 用户信息卡片 -->
    <view class="user-card">
      <view class="user-info">
        <image 
          :src="userInfo.avatar_url || '/static/default-avatar.png'" 
          class="avatar"
          mode="aspectFill"
        />
        <view class="user-details">
          <text class="nickname">{{ userInfo.nickname || '未设置昵称' }}</text>
          <text class="phone">{{ userInfo.phone_number || '未绑定手机' }}</text>
          <view class="status-badge" :class="getStatusClass(userInfo.status)">
            {{ getStatusText(userInfo.status) }}
          </view>
        </view>
      </view>
      
      <!-- 授权按钮 -->
      <button 
        v-if="!userInfo.nickname || !userInfo.avatar_url"
        class="auth-btn"
        @click="getUserProfile"
        open-type="getUserProfile"
      >
        授权获取头像昵称
      </button>
    </view>

    <!-- 统计信息 -->
    <view class="stats-card">
      <view class="stats-grid">
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.publishing_credits || 0 }}</text>
          <text class="stat-label">剩余发布次数</text>
        </view>
        <view class="stat-item">
          <text class="stat-number">{{ userInfo.statistics?.total_listings || 0 }}</text>
          <text class="stat-label">累计发布</text>
        </view>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="goToMyListings">
        <view class="menu-icon">📋</view>
        <text class="menu-title">我的发布</text>
        <text class="menu-desc">管理我发布的信息</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPublish">
        <view class="menu-icon">➕</view>
        <text class="menu-title">发布信息</text>
        <text class="menu-desc">发布新的转让信息</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToPackages">
        <view class="menu-icon">💎</view>
        <text class="menu-title">购买套餐</text>
        <text class="menu-desc">增加发布次数</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="goToInvite">
        <view class="menu-icon">👥</view>
        <text class="menu-title">邀请好友</text>
        <text class="menu-desc">邀请好友获得奖励</text>
        <text class="menu-arrow">></text>
      </view>

      <view class="menu-item" @click="shareApp">
        <view class="menu-icon">📤</view>
        <text class="menu-title">分享应用</text>
        <text class="menu-desc">分享给更多朋友</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 设置菜单 -->
    <view class="menu-section">
      <view class="menu-item" @click="updateProfile">
        <view class="menu-icon">⚙️</view>
        <text class="menu-title">个人设置</text>
        <text class="menu-desc">修改个人信息</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="contactService">
        <view class="menu-icon">📞</view>
        <text class="menu-title">联系客服</text>
        <text class="menu-desc">遇到问题联系我们</text>
        <text class="menu-arrow">></text>
      </view>
      
      <view class="menu-item" @click="logout">
        <view class="menu-icon">🚪</view>
        <text class="menu-title">退出登录</text>
        <text class="menu-desc">安全退出当前账号</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import { userAPI, authAPI, utils } from '@/utils/api.js'

// 响应式数据
const userInfo = ref({})
const isLoading = ref(false)

// 生命周期
onShow(() => {
  loadUserInfo()
})

// 方法定义
const loadUserInfo = async () => {
  try {
    isLoading.value = true
    
    const response = await userAPI.getCurrentUser()
    
    if (response.success) {
      userInfo.value = response.data
    } else {
      utils.handleError(new Error(response.message), '获取用户信息失败')
    }
    
  } catch (error) {
    utils.handleError(error, '获取用户信息失败')
  } finally {
    isLoading.value = false
  }
}

const getUserProfile = () => {
  uni.getUserProfile({
    desc: '用于完善用户资料',
    success: async (res) => {
      try {
        const { nickName, avatarUrl } = res.userInfo
        
        const response = await userAPI.updateUser({
          nickname: nickName,
          avatar_url: avatarUrl
        })
        
        if (response.success) {
          uni.showToast({
            title: '授权成功',
            icon: 'success'
          })
          
          // 更新本地用户信息
          userInfo.value.nickname = nickName
          userInfo.value.avatar_url = avatarUrl
        } else {
          utils.handleError(new Error(response.message), '更新用户信息失败')
        }
        
      } catch (error) {
        utils.handleError(error, '更新用户信息失败')
      }
    },
    fail: () => {
      uni.showToast({
        title: '授权失败',
        icon: 'none'
      })
    }
  })
}

const goToMyListings = () => {
  uni.navigateTo({
    url: '/pages/user/my-listings'
  })
}

const goToPublish = () => {
  uni.navigateTo({
    url: '/pages/listings/publish'
  })
}

const goToPackages = () => {
  uni.navigateTo({
    url: '/pages/packages/packages'
  })
}

const goToInvite = () => {
  uni.navigateTo({
    url: '/pages/invite/invite'
  })
}

const shareApp = () => {
  // 生成带邀请者ID的分享链接
  const inviterId = userInfo.value.id
  const shareUrl = `https://your-miniprogram.com/pages/login/login?inviterId=${inviterId}`

  uni.share({
    provider: 'weixin',
    scene: 'WXSceneSession',
    type: 0,
    href: shareUrl,
    title: '甩单系统',
    summary: '发现更多商机，快来加入甩单系统！',
    imageUrl: '/static/share-logo.png',
    success: () => {
      uni.showToast({
        title: '分享成功',
        icon: 'success'
      })
    },
    fail: () => {
      // 降级到复制链接
      uni.setClipboardData({
        data: shareUrl,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          })
        }
      })
    }
  })
}

const updateProfile = () => {
  uni.showModal({
    title: '修改手机号',
    editable: true,
    placeholderText: '请输入手机号',
    success: async (res) => {
      if (res.confirm && res.content) {
        try {
          const response = await userAPI.updateUser({
            phone_number: res.content
          })
          
          if (response.success) {
            uni.showToast({
              title: '修改成功',
              icon: 'success'
            })
            
            userInfo.value.phone_number = res.content
          } else {
            utils.handleError(new Error(response.message), '修改失败')
          }
          
        } catch (error) {
          utils.handleError(error, '修改失败')
        }
      }
    }
  })
}

const contactService = () => {
  uni.showModal({
    title: '联系客服',
    content: '客服微信：service123\n客服电话：************',
    showCancel: false
  })
}

const logout = () => {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          await authAPI.logout()
        } catch (error) {
          console.log('登出接口调用失败，但继续清除本地数据')
        }
        
        // 清除本地数据
        utils.clearAuth()
        
        // 跳转到登录页
        uni.reLaunch({
          url: '/pages/login/login'
        })
      }
    }
  })
}

// 工具方法
const getStatusClass = (status) => {
  return status === 'active' ? 'status-active' : 'status-inactive'
}

const getStatusText = (status) => {
  return status === 'active' ? '已激活' : '未激活'
}
</script>

<style scoped>
.profile-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.user-card {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 30rpx;
  background: #f0f0f0;
}

.user-details {
  flex: 1;
}

.nickname {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.phone {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.status-badge {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.status-active {
  background: #22c55e;
}

.status-inactive {
  background: #f59e0b;
}

.auth-btn {
  width: 100%;
  height: 80rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.stats-card {
  background: white;
  border-radius: 12rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.stats-grid {
  display: flex;
  justify-content: space-around;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #22c55e;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 26rpx;
  color: #666;
}

.menu-section {
  background: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  font-size: 40rpx;
  margin-right: 30rpx;
  width: 60rpx;
  text-align: center;
}

.menu-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.menu-desc {
  font-size: 24rpx;
  color: #999;
  flex: 1;
  margin-left: 20rpx;
}

.menu-arrow {
  font-size: 28rpx;
  color: #ccc;
  margin-left: auto;
}

.menu-item:active {
  background: #f9f9f9;
}
</style>
