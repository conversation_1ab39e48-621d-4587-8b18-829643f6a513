<template>
  <view class="my-listings-container">
    <!-- 头部统计 -->
    <view class="header-stats">
      <view class="stat-item">
        <text class="stat-number">{{ totalCount }}</text>
        <text class="stat-label">总发布</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ sellingCount }}</text>
        <text class="stat-label">在售中</text>
      </view>
      <view class="stat-item">
        <text class="stat-number">{{ soldCount }}</text>
        <text class="stat-label">已售出</text>
      </view>
    </view>

    <!-- 状态筛选 -->
    <view class="status-filter">
      <view 
        v-for="(status, index) in statusOptions" 
        :key="index"
        class="status-tab"
        :class="{ active: selectedStatus === status.value }"
        @click="changeStatus(status.value)"
      >
        <text>{{ status.label }}</text>
      </view>
    </view>

    <!-- 列表区域 -->
    <scroll-view 
      class="listings-scroll"
      scroll-y
      @scrolltolower="loadMore"
      :refresher-enabled="true"
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="listings-list">
        <view 
          v-for="item in listings" 
          :key="item.id"
          class="listing-item"
        >
          <view class="listing-header" @click="goToDetail(item.id)">
            <text class="company-name">{{ item.company_name }}</text>
            <view class="status-tag" :class="getStatusClass(item.status)">
              {{ item.status }}
            </view>
          </view>
          
          <view class="listing-info" @click="goToDetail(item.id)">
            <view class="info-row">
              <text class="label">类型：</text>
              <text class="value">{{ item.listing_type }}</text>
            </view>
            <view class="info-row">
              <text class="label">地区：</text>
              <text class="value">{{ item.registration_city || '未填写' }}</text>
            </view>
            <view class="info-row">
              <text class="label">发布时间：</text>
              <text class="value">{{ formatTime(item.created_at) }}</text>
            </view>
          </view>
          
          <view class="listing-footer">
            <view class="price-section">
              <text v-if="item.is_negotiable" class="price negotiable">面议</text>
              <text v-else-if="item.price" class="price">¥{{ formatPrice(item.price) }}</text>
              <text v-else class="price negotiable">面议</text>
            </view>
            
            <view class="action-buttons">
              <button 
                v-if="item.status === '在售'" 
                class="action-btn edit-btn" 
                @click="editListing(item.id)"
              >
                编辑
              </button>
              <button 
                v-if="item.status === '在售'" 
                class="action-btn offline-btn" 
                @click="changeListingStatus(item.id, '下架')"
              >
                下架
              </button>
              <button 
                v-if="item.status === '下架'" 
                class="action-btn online-btn" 
                @click="changeListingStatus(item.id, '在售')"
              >
                上架
              </button>
              <button 
                v-if="item.status !== '已售'" 
                class="action-btn sold-btn" 
                @click="changeListingStatus(item.id, '已售')"
              >
                标记已售
              </button>
              <button 
                class="action-btn delete-btn" 
                @click="deleteListing(item.id)"
              >
                删除
              </button>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-section" v-if="isLoading">
        <text class="loading-text">加载中...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more-section" v-if="!hasMore && listings.length > 0">
        <text class="no-more-text">没有更多数据了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-section" v-if="!isLoading && listings.length === 0">
        <text class="empty-text">暂无发布记录</text>
        <button class="publish-btn" @click="goToPublish">立即发布</button>
      </view>
    </scroll-view>

    <!-- 悬浮发布按钮 -->
    <view class="fab" @click="goToPublish">
      <text class="fab-icon">+</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { userAPI, listingAPI, utils } from '@/utils/api.js'

// 响应式数据
const listings = ref([])
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)
const selectedStatus = ref('')

// 分页参数
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
})

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '在售', value: '在售' },
  { label: '已售', value: '已售' },
  { label: '下架', value: '下架' }
]

// 计算属性
const totalCount = computed(() => listings.value.length)
const sellingCount = computed(() => listings.value.filter(item => item.status === '在售').length)
const soldCount = computed(() => listings.value.filter(item => item.status === '已售').length)

// 生命周期
onMounted(() => {
  loadMyListings()
})

// 方法定义
const loadMyListings = async (isRefresh = false) => {
  if (isLoading.value) return
  
  try {
    isLoading.value = true
    
    if (isRefresh) {
      pagination.page = 1
      hasMore.value = true
    }
    
    // 构建查询参数
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize
    }
    
    // 添加状态筛选
    if (selectedStatus.value) {
      params.status = selectedStatus.value
    }
    
    const response = await userAPI.getMyListings(params)
    
    if (response.success) {
      const newListings = response.data || []
      
      if (isRefresh) {
        listings.value = newListings
      } else {
        listings.value = [...listings.value, ...newListings]
      }
      
      // 更新分页信息
      if (response.pagination) {
        pagination.total = response.pagination.total
        hasMore.value = pagination.page < response.pagination.totalPages
      }
    }
    
  } catch (error) {
    utils.handleError(error, '获取我的发布失败')
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

const loadMore = () => {
  if (!hasMore.value || isLoading.value) return
  
  pagination.page++
  loadMyListings()
}

const onRefresh = () => {
  isRefreshing.value = true
  loadMyListings(true)
}

const changeStatus = (status) => {
  selectedStatus.value = status
  loadMyListings(true)
}

const goToDetail = (id) => {
  uni.navigateTo({
    url: `/pages/listings/detail?id=${id}`
  })
}

const goToPublish = () => {
  uni.navigateTo({
    url: '/pages/listings/publish'
  })
}

const editListing = (id) => {
  uni.navigateTo({
    url: `/pages/listings/edit?id=${id}`
  })
}

const changeListingStatus = async (id, newStatus) => {
  try {
    uni.showModal({
      title: '确认操作',
      content: `确定要${newStatus === '已售' ? '标记为已售' : newStatus}吗？`,
      success: async (res) => {
        if (res.confirm) {
          const response = await listingAPI.updateListing(id, { status: newStatus })
          
          if (response.success) {
            uni.showToast({
              title: '操作成功',
              icon: 'success'
            })
            
            // 更新本地数据
            const index = listings.value.findIndex(item => item.id === id)
            if (index !== -1) {
              listings.value[index].status = newStatus
            }
          } else {
            utils.handleError(new Error(response.message), '操作失败')
          }
        }
      }
    })
  } catch (error) {
    utils.handleError(error, '操作失败')
  }
}

const deleteListing = async (id) => {
  try {
    uni.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这条信息吗？',
      success: async (res) => {
        if (res.confirm) {
          const response = await listingAPI.deleteListing(id)
          
          if (response.success) {
            uni.showToast({
              title: '删除成功',
              icon: 'success'
            })
            
            // 从列表中移除
            const index = listings.value.findIndex(item => item.id === id)
            if (index !== -1) {
              listings.value.splice(index, 1)
            }
          } else {
            utils.handleError(new Error(response.message), '删除失败')
          }
        }
      }
    })
  } catch (error) {
    utils.handleError(error, '删除失败')
  }
}

// 工具方法
const getStatusClass = (status) => {
  const classMap = {
    '在售': 'status-selling',
    '已售': 'status-sold',
    '下架': 'status-offline'
  }
  return classMap[status] || 'status-default'
}

const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  return date.toLocaleDateString('zh-CN')
}

const formatPrice = (price) => {
  if (!price) return '0'
  return Number(price).toLocaleString('zh-CN')
}
</script>

<style scoped>
.my-listings-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  position: relative;
}

.header-stats {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  border-bottom: 1rpx solid #eee;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #22c55e;
  margin-bottom: 10rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

.status-filter {
  background: white;
  padding: 0 30rpx;
  display: flex;
  border-bottom: 1rpx solid #eee;
}

.status-tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 3rpx solid transparent;
}

.status-tab.active {
  color: #22c55e;
  border-bottom-color: #22c55e;
}

.listings-scroll {
  flex: 1;
}

.listings-list {
  padding: 20rpx;
}

.listing-item {
  background: white;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.listing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.status-tag {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  color: white;
}

.status-selling {
  background: #22c55e;
}

.status-sold {
  background: #ef4444;
}

.status-offline {
  background: #6b7280;
}

.listing-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.label {
  color: #666;
  width: 140rpx;
}

.value {
  color: #333;
  flex: 1;
}

.listing-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price {
  font-size: 30rpx;
  font-weight: bold;
  color: #ef4444;
}

.price.negotiable {
  color: #f59e0b;
}

.action-buttons {
  display: flex;
  gap: 15rpx;
}

.action-btn {
  padding: 10rpx 20rpx;
  border: none;
  border-radius: 6rpx;
  font-size: 24rpx;
  color: white;
}

.edit-btn {
  background: #3b82f6;
}

.offline-btn {
  background: #6b7280;
}

.online-btn {
  background: #22c55e;
}

.sold-btn {
  background: #f59e0b;
}

.delete-btn {
  background: #ef4444;
}

.loading-section,
.no-more-section,
.empty-section {
  padding: 40rpx;
  text-align: center;
}

.loading-text,
.no-more-text,
.empty-text {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 20rpx;
}

.publish-btn {
  padding: 20rpx 40rpx;
  background: #22c55e;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.fab {
  position: fixed;
  right: 40rpx;
  bottom: 40rpx;
  width: 120rpx;
  height: 120rpx;
  background: #22c55e;
  border-radius: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(34, 197, 94, 0.3);
  z-index: 999;
}

.fab-icon {
  font-size: 48rpx;
  color: white;
  font-weight: bold;
}
</style>
