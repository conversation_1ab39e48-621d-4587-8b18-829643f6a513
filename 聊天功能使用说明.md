# 甩单系统聊天功能使用说明

## 功能概述

本次开发实现了甩单系统的聊天功能，包括：

### 已完成功能 ✅

1. **登录页面**
   - 绿色圆形logo设计
   - 手机号快捷登录（集成微信登录API）
   - 暂不登录选项
   - 服务条款和隐私政策链接

2. **消息界面**
   - 顶部群组选择器（下拉菜单）
   - 搜索和用户头像按钮
   - 消息列表显示（支持系统消息和用户消息）
   - 5个功能按钮（公司转让、本地同行、挂牌交易、黄光、群公告）
   - 消息输入和发送功能
   - 私信按钮

3. **服务端API**
   - 聊天群组管理API
   - 消息发送/接收API
   - 用户群组关系管理API
   - 轮询机制替代WebSocket

4. **数据模型**
   - ChatGroup（聊天群组）
   - GroupMember（群组成员）
   - Message（消息）

## 项目结构

```
shuaiDanSystem/
├── server/                     # 服务端
│   ├── models/                 # 数据模型
│   │   ├── ChatGroup.js       # 群组模型
│   │   ├── GroupMember.js     # 群组成员模型
│   │   └── Message.js         # 消息模型
│   ├── routes/
│   │   └── chat.js            # 聊天相关路由
│   ├── scripts/
│   │   ├── init-chat-data.js  # 聊天数据初始化
│   │   └── create-sample-data.sql # 示例数据SQL
│   └── test/
│       └── chat-api-test.js   # 聊天API测试
├── shuaiDanClient/            # 小程序端
│   ├── pages/
│   │   ├── login/
│   │   │   └── login.vue      # 登录页面
│   │   └── chat/
│   │       └── chat.vue       # 消息页面
│   └── utils/
│       └── api.js             # API工具类
└── sql.sql                    # 数据库表结构
```

## 使用方法

### 1. 服务端启动

```bash
cd server
npm install
npm run dev
```

### 2. 数据库初始化

执行以下SQL文件来创建表结构和示例数据：

```sql
-- 1. 先执行主表结构
source sql.sql

-- 2. 再执行示例数据
source server/scripts/create-sample-data.sql
```

### 3. 小程序端

1. 使用HBuilderX或其他uni-app开发工具打开 `shuaiDanClient` 目录
2. 运行到微信开发者工具或浏览器进行测试

### 4. API测试

```bash
cd server
node test/chat-api-test.js
```

## API接口说明

### 聊天相关接口

| 接口 | 方法 | 说明 |
|------|------|------|
| `/api/v1/chat/groups` | GET | 获取群组列表 |
| `/api/v1/chat/my-groups` | GET | 获取我的群组 |
| `/api/v1/chat/groups/:id` | GET | 获取群组详情 |
| `/api/v1/chat/groups/:id/join` | POST | 加入群组 |
| `/api/v1/chat/groups/:id/leave` | POST | 退出群组 |
| `/api/v1/chat/groups/:id/members` | GET | 获取群组成员 |
| `/api/v1/chat/groups/:id/messages` | POST | 发送消息 |
| `/api/v1/chat/groups/:id/messages` | GET | 获取消息列表 |
| `/api/v1/chat/groups/:id/new-messages` | GET | 获取新消息（轮询） |

## 功能特点

### 1. 轮询机制
- 每3秒自动获取新消息
- 避免WebSocket的复杂性
- 适合小程序环境

### 2. 响应式设计
- 适配不同屏幕尺寸
- 符合微信小程序设计规范

### 3. 用户体验
- 登录状态检查
- 错误处理和提示
- 消息自动滚动到底部

### 4. 数据安全
- JWT认证
- 群组成员权限验证
- 输入内容验证

## 待开发功能

### 下一阶段计划

1. **功能按钮实现**
   - 公司转让页面
   - 本地同行功能
   - 挂牌交易页面
   - 黄光功能
   - 群公告管理

2. **高级功能**
   - 私信功能
   - 文件/图片发送
   - 消息搜索
   - 群组管理

3. **性能优化**
   - 消息分页加载
   - 图片懒加载
   - 缓存机制

## 注意事项

1. **数据库连接**
   - 确保MySQL服务正常运行
   - 检查数据库连接配置（server/.env）

2. **微信小程序配置**
   - 需要配置正确的AppID和AppSecret
   - 开发时需要关闭域名校验

3. **网络请求**
   - 小程序端API请求地址需要根据实际部署调整
   - 生产环境需要使用HTTPS

4. **权限管理**
   - 未登录用户只能查看消息，不能发送
   - 群组成员才能发送消息

## 技术栈

- **后端**: Node.js + Express + MySQL
- **前端**: uni-app + Vue 3
- **认证**: JWT
- **数据库**: MySQL 8.0+
- **通信**: RESTful API + 轮询

## 联系方式

如有问题请联系开发团队。
